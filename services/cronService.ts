import cron from 'node-cron';
import { notificationService } from './notificationService';
import { EventCacheModel } from '../models/rsvpModels';

class CronService {
  private jobs: Map<string, cron.ScheduledTask> = new Map();

  /**
   * Initialize all cron jobs
   */
  init(): void {
    this.scheduleNotificationProcessor();
    this.scheduleEventCleanup();
    this.scheduleHealthCheck();
    
    console.log('✅ RSVP Cron jobs initialized');
  }

  /**
   * Process pending notifications every 5 minutes
   */
  private scheduleNotificationProcessor(): void {
    const task = cron.schedule('*/5 * * * *', async () => {
      try {
        console.log('🔔 Processing pending notifications...');
        await notificationService.processPendingNotifications(100);
        
        const stats = await notificationService.getNotificationStats();
        console.log(`📊 Notification stats: ${stats.pending} pending, ${stats.sent} sent, ${stats.failed} failed`);
        
        if (stats.overdue > 0) {
          console.warn(`⚠️ ${stats.overdue} overdue notifications found`);
        }
      } catch (error) {
        console.error('❌ Error processing notifications:', error);
      }
    }, {
      scheduled: false,
      timezone: "UTC"
    });

    this.jobs.set('notificationProcessor', task);
    task.start();
    console.log('📅 Notification processor scheduled (every 5 minutes)');
  }

  /**
   * Clean up expired events daily at 2 AM UTC
   */
  private scheduleEventCleanup(): void {
    const task = cron.schedule('0 2 * * *', async () => {
      try {
        console.log('🧹 Cleaning up expired events...');
        const deletedCount = await EventCacheModel.cleanupExpiredEvents();
        console.log(`🗑️ Cleaned up ${deletedCount} expired events`);
      } catch (error) {
        console.error('❌ Error cleaning up events:', error);
      }
    }, {
      scheduled: false,
      timezone: "UTC"
    });

    this.jobs.set('eventCleanup', task);
    task.start();
    console.log('📅 Event cleanup scheduled (daily at 2 AM UTC)');
  }

  /**
   * Health check every hour
   */
  private scheduleHealthCheck(): void {
    const task = cron.schedule('0 * * * *', async () => {
      try {
        const stats = await notificationService.getNotificationStats();
        
        // Log health metrics
        console.log('💓 RSVP System Health Check:', {
          timestamp: new Date().toISOString(),
          notifications: stats,
          uptime: process.uptime(),
        });

        // Alert if too many failed notifications
        if (stats.failed > 100) {
          console.error(`🚨 High failure rate: ${stats.failed} failed notifications`);
        }

        // Alert if too many overdue notifications
        if (stats.overdue > 50) {
          console.error(`🚨 Many overdue notifications: ${stats.overdue} overdue`);
        }
      } catch (error) {
        console.error('❌ Health check failed:', error);
      }
    }, {
      scheduled: false,
      timezone: "UTC"
    });

    this.jobs.set('healthCheck', task);
    task.start();
    console.log('📅 Health check scheduled (hourly)');
  }

  /**
   * Stop all cron jobs
   */
  stopAll(): void {
    this.jobs.forEach((task, name) => {
      task.stop();
      console.log(`⏹️ Stopped cron job: ${name}`);
    });
    this.jobs.clear();
  }

  /**
   * Stop specific cron job
   */
  stop(jobName: string): void {
    const task = this.jobs.get(jobName);
    if (task) {
      task.stop();
      this.jobs.delete(jobName);
      console.log(`⏹️ Stopped cron job: ${jobName}`);
    }
  }

  /**
   * Get status of all jobs
   */
  getStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    this.jobs.forEach((task, name) => {
      status[name] = task.running;
    });
    return status;
  }

  /**
   * Manually trigger notification processing
   */
  async triggerNotificationProcessing(): Promise<void> {
    console.log('🔔 Manually triggering notification processing...');
    await notificationService.processPendingNotifications(100);
  }

  /**
   * Manually trigger event cleanup
   */
  async triggerEventCleanup(): Promise<void> {
    console.log('🧹 Manually triggering event cleanup...');
    const deletedCount = await EventCacheModel.cleanupExpiredEvents();
    console.log(`🗑️ Cleaned up ${deletedCount} expired events`);
  }
}

export const cronService = new CronService();
export default cronService;
